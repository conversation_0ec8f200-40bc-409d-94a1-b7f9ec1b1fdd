我有一台已在 DigitalOcean 部署 n8n 的 Ubuntu 服务器，当前已有普通用户 "xubin"，域名 n8n.eric-n8n.online。
请帮我生成一个完整的 Docker Compose 部署方案，使得：
- 检查是否存在用户 "xubin" 并且已在 sudo 组且配置好 SSH 公钥；如果不存在，则执行 adduser 和 usermod 来创建并配置该用户；
- 同时在同一服务器上部署 n8n 和 Postiz，两者互不干扰；
- n8n 保持现有域名 n8n.eric-n8n.online，Postiz 使用子域 postiz.eric-n8n.online；
- 使用 Caddy 作为反向代理，自动管理 HTTPS 证书；
- 每个服务使用独立的 Docker 网络、数据卷和环境变量；
- 提供创建用户、安装、启动、更新（单服务重启）和日志查看的具体命令；
- 最后给出完整的 `docker-compose.yml`、`Caddyfile` 以及 `.env` 样板；
- 引用 n8n 官方文档：https://docs.n8n.io/hosting/installation/server-setups/digital-ocean/  
- 引用 Postiz 安装文档：https://docs.postiz.com/installation/docker-compose
重点参考官方文档，自己不要瞎猜，需要什么资料跟我说
完成动作更新readme文档