#!/bin/bash

# 配置 Docker 网络和数据卷脚本
# 为 n8n 和 Postiz 创建独立的网络和数据卷

set -e

echo "=== 配置 Docker 网络和数据卷 ==="

# 检查是否以 xubin 用户运行
if [ "$USER" != "xubin" ]; then
    echo "请以 xubin 用户运行此脚本"
    echo "使用: su - xubin 然后运行 bash setup-docker.sh"
    exit 1
fi

# 检查 Docker 是否可用
if ! docker info &>/dev/null; then
    echo "❌ Docker 不可用，请检查 Docker 安装和用户权限"
    echo "可能需要重新登录以应用 docker 组权限"
    exit 1
fi

echo "✓ Docker 可用"

# 创建 Docker 网络
echo ""
echo "创建 Docker 网络..."

# 创建 n8n 网络
if docker network ls | grep -q "n8n-network"; then
    echo "✓ n8n-network 已存在"
else
    docker network create n8n-network
    echo "✓ 创建 n8n-network"
fi

# 创建 Postiz 网络
if docker network ls | grep -q "postiz-network"; then
    echo "✓ postiz-network 已存在"
else
    docker network create postiz-network
    echo "✓ 创建 postiz-network"
fi

# 创建共享网络（用于 Caddy 访问两个服务）
if docker network ls | grep -q "proxy-network"; then
    echo "✓ proxy-network 已存在"
else
    docker network create proxy-network
    echo "✓ 创建 proxy-network"
fi

# 创建 Docker 数据卷
echo ""
echo "创建 Docker 数据卷..."

# n8n 相关数据卷
volumes=(
    "n8n_data"
    "n8n_postgres_data"
    "caddy_data"
    "caddy_config"
    "postiz_data"
    "postiz_postgres_data"
    "postiz_redis_data"
    "postiz_uploads"
)

for volume in "${volumes[@]}"; do
    if docker volume ls | grep -q "$volume"; then
        echo "✓ $volume 已存在"
    else
        docker volume create "$volume"
        echo "✓ 创建 $volume"
    fi
done

# 显示创建的网络和数据卷
echo ""
echo "=== Docker 网络列表 ==="
docker network ls | grep -E "(n8n-network|postiz-network|proxy-network)"

echo ""
echo "=== Docker 数据卷列表 ==="
docker volume ls | grep -E "(n8n|postiz|caddy)"

# 创建网络和数据卷信息文件
cat > "$HOME/automation-stack/docker-info.md" << 'EOF'
# Docker 网络和数据卷配置

## 网络配置

### n8n-network
- 用途: n8n 服务内部通信
- 包含服务: n8n, n8n-postgres

### postiz-network  
- 用途: Postiz 服务内部通信
- 包含服务: postiz, postiz-postgres, postiz-redis

### proxy-network
- 用途: Caddy 反向代理网络
- 包含服务: caddy, n8n, postiz

## 数据卷配置

### n8n 相关
- `n8n_data`: n8n 应用数据（工作流、凭据等）
- `n8n_postgres_data`: n8n PostgreSQL 数据库数据

### Postiz 相关
- `postiz_data`: Postiz 应用配置数据
- `postiz_postgres_data`: Postiz PostgreSQL 数据库数据
- `postiz_redis_data`: Postiz Redis 缓存数据
- `postiz_uploads`: Postiz 上传文件存储

### Caddy 相关
- `caddy_data`: Caddy 数据（SSL 证书等）
- `caddy_config`: Caddy 配置文件

## 网络隔离说明

1. n8n 和 Postiz 使用独立的内部网络，确保服务隔离
2. 两个服务都连接到 proxy-network，允许 Caddy 访问
3. 数据库和缓存只在各自的内部网络中可访问
4. 外部只能通过 Caddy 反向代理访问服务
EOF

echo ""
echo "✓ Docker 网络和数据卷配置完成！"
echo ""
echo "配置信息已保存到: $HOME/automation-stack/docker-info.md"
echo ""
echo "下一步: 生成环境变量配置"
