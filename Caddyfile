# =============================================================================
# Caddy 反向代理配置
# 支持 n8n 和 Postiz 双域名部署
# =============================================================================

# n8n 服务配置
n8n.eric-n8n.online {
    # 反向代理到 n8n 服务
    reverse_proxy n8n:5678 {
        # 禁用缓冲以支持实时通信
        flush_interval -1
        
        # 设置超时
        timeout 300s
        
        # 传递原始主机头
        header_up Host {host}
        header_up X-Real-IP {remote}
        header_up X-Forwarded-For {remote}
        header_up X-Forwarded-Proto {scheme}
    }
    
    # 启用访问日志
    log {
        output file /var/log/caddy/n8n-access.log
        format json
    }
    
    # 安全头设置
    header {
        # 启用 HSTS
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        
        # 防止点击劫持
        X-Frame-Options "SAMEORIGIN"
        
        # 防止 MIME 类型嗅探
        X-Content-Type-Options "nosniff"
        
        # XSS 保护
        X-XSS-Protection "1; mode=block"
        
        # 引用策略
        Referrer-Policy "strict-origin-when-cross-origin"
    }
}

# Postiz 服务配置
postiz.eric-n8n.online {
    # 反向代理到 Postiz 服务
    reverse_proxy postiz:5000 {
        # 禁用缓冲以支持实时通信
        flush_interval -1
        
        # 设置超时
        timeout 300s
        
        # 传递原始主机头
        header_up Host {host}
        header_up X-Real-IP {remote}
        header_up X-Forwarded-For {remote}
        header_up X-Forwarded-Proto {scheme}
    }
    
    # 启用访问日志
    log {
        output file /var/log/caddy/postiz-access.log
        format json
    }
    
    # 安全头设置
    header {
        # 启用 HSTS
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        
        # 防止点击劫击
        X-Frame-Options "SAMEORIGIN"
        
        # 防止 MIME 类型嗅探
        X-Content-Type-Options "nosniff"
        
        # XSS 保护
        X-XSS-Protection "1; mode=block"
        
        # 引用策略
        Referrer-Policy "strict-origin-when-cross-origin"
    }
}

# =============================================================================
# 全局配置
# =============================================================================

# 全局选项
{
    # 管理员邮箱（用于 Let's Encrypt）
    email <EMAIL>
    
    # 启用管理 API
    admin localhost:2019
    
    # 日志配置
    log {
        output file /var/log/caddy/caddy.log
        level INFO
        format json
    }
    
    # 自动 HTTPS 配置
    auto_https on
    
    # 服务器配置
    servers {
        protocol {
            experimental_http3
        }
    }
}

# =============================================================================
# 可选：重定向配置
# =============================================================================

# 将根域名重定向到 n8n（可选）
# eric-n8n.online {
#     redir https://n8n.eric-n8n.online{uri} permanent
# }

# 将 www 子域名重定向到对应服务（可选）
# www.eric-n8n.online {
#     redir https://n8n.eric-n8n.online{uri} permanent
# }
