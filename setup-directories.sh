#!/bin/bash

# 创建项目目录结构脚本
# 为 n8n 和 Postiz 创建独立的目录结构

set -e

echo "=== 创建项目目录结构 ==="

# 检查是否以 xubin 用户运行
if [ "$USER" != "xubin" ]; then
    echo "请以 xubin 用户运行此脚本"
    echo "使用: su - xubin 然后运行 bash setup-directories.sh"
    exit 1
fi

# 主项目目录
PROJECT_DIR="$HOME/automation-stack"

echo "创建主项目目录: $PROJECT_DIR"
mkdir -p "$PROJECT_DIR"
cd "$PROJECT_DIR"

# 创建 n8n 相关目录
echo "创建 n8n 目录结构..."
mkdir -p n8n/{config,data,local_files}
mkdir -p caddy/{config,data}

# 创建 Postiz 相关目录
echo "创建 Postiz 目录结构..."
mkdir -p postiz/{config,uploads}

# 创建数据库目录
echo "创建数据库目录..."
mkdir -p databases/{n8n-postgres,postiz-postgres,redis}

# 创建日志目录
echo "创建日志目录..."
mkdir -p logs/{n8n,postiz,caddy}

# 创建备份目录
echo "创建备份目录..."
mkdir -p backups/{n8n,postiz}

# 创建脚本目录
echo "创建脚本目录..."
mkdir -p scripts

# 设置目录权限
echo "设置目录权限..."
chmod -R 755 "$PROJECT_DIR"

# 创建目录结构说明文件
cat > "$PROJECT_DIR/directory-structure.md" << 'EOF'
# 自动化技术栈目录结构

```
automation-stack/
├── n8n/                    # n8n 相关文件
│   ├── config/            # n8n 配置文件
│   ├── data/              # n8n 数据（工作流等）
│   └── local_files/       # n8n 本地文件存储
├── postiz/                # Postiz 相关文件
│   ├── config/            # Postiz 配置文件
│   └── uploads/           # Postiz 上传文件
├── caddy/                 # Caddy 反向代理
│   ├── config/            # Caddy 配置文件
│   └── data/              # Caddy 数据（证书等）
├── databases/             # 数据库数据
│   ├── n8n-postgres/      # n8n PostgreSQL 数据
│   ├── postiz-postgres/   # Postiz PostgreSQL 数据
│   └── redis/             # Redis 数据
├── logs/                  # 日志文件
│   ├── n8n/               # n8n 日志
│   ├── postiz/            # Postiz 日志
│   └── caddy/             # Caddy 日志
├── backups/               # 备份文件
│   ├── n8n/               # n8n 备份
│   └── postiz/            # Postiz 备份
├── scripts/               # 管理脚本
├── docker-compose.yml     # Docker Compose 配置
├── .env                   # 环境变量
└── Caddyfile             # Caddy 配置
```

## 说明

- 每个服务都有独立的配置和数据目录
- 使用独立的数据库实例避免冲突
- 日志和备份分别存储便于管理
- 所有配置文件集中在项目根目录
EOF

echo ""
echo "✓ 目录结构创建完成！"
echo ""
echo "项目目录: $PROJECT_DIR"
echo ""
echo "目录结构："
tree "$PROJECT_DIR" 2>/dev/null || find "$PROJECT_DIR" -type d | sed 's|[^/]*/|  |g'
echo ""
echo "下一步: 配置 Docker 网络和数据卷"
