#!/bin/bash

# =============================================================================
# 自动化技术栈一键部署脚本
# 自动完成整个部署流程
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查用户
check_user() {
    if [ "$USER" != "xubin" ]; then
        log_error "请以 xubin 用户运行此脚本"
        log_info "使用: su - xubin 然后运行 bash deploy.sh"
        exit 1
    fi
}

# 检查 Docker
check_docker() {
    log_info "检查 Docker 环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! docker info &>/dev/null; then
        log_error "Docker 不可用，请检查 Docker 服务和用户权限"
        log_info "可能需要重新登录以应用 docker 组权限"
        exit 1
    fi
    
    if ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    log_success "Docker 环境检查通过"
}

# 设置项目目录
setup_project() {
    local project_dir="$HOME/automation-stack"
    
    log_info "设置项目目录: $project_dir"
    
    if [ ! -d "$project_dir" ]; then
        mkdir -p "$project_dir"
        log_success "项目目录创建成功"
    else
        log_info "项目目录已存在"
    fi
    
    cd "$project_dir"
    
    # 复制配置文件到项目目录
    if [ -f "$HOME/docker-compose.yml" ]; then
        cp "$HOME/docker-compose.yml" .
        log_info "复制 docker-compose.yml"
    fi
    
    if [ -f "$HOME/.env" ]; then
        cp "$HOME/.env" .
        log_info "复制 .env"
    fi
    
    if [ -f "$HOME/Caddyfile" ]; then
        cp "$HOME/Caddyfile" .
        log_info "复制 Caddyfile"
    fi
    
    if [ -f "$HOME/manage.sh" ]; then
        cp "$HOME/manage.sh" .
        chmod +x manage.sh
        log_info "复制管理脚本"
    fi
}

# 创建 Docker 网络和数据卷
setup_docker_resources() {
    log_info "创建 Docker 网络和数据卷..."
    
    # 创建网络
    networks=("n8n-network" "postiz-network" "proxy-network")
    for network in "${networks[@]}"; do
        if ! docker network ls | grep -q "$network"; then
            docker network create "$network"
            log_info "创建网络: $network"
        else
            log_info "网络已存在: $network"
        fi
    done
    
    # 创建数据卷
    volumes=(
        "n8n_data"
        "n8n_postgres_data"
        "caddy_data"
        "caddy_config"
        "postiz_data"
        "postiz_postgres_data"
        "postiz_redis_data"
        "postiz_uploads"
    )
    
    for volume in "${volumes[@]}"; do
        if ! docker volume ls | grep -q "$volume"; then
            docker volume create "$volume"
            log_info "创建数据卷: $volume"
        else
            log_info "数据卷已存在: $volume"
        fi
    done
    
    log_success "Docker 资源创建完成"
}

# 检查环境变量
check_env_vars() {
    log_info "检查环境变量配置..."
    
    if [ ! -f ".env" ]; then
        log_error ".env 文件不存在"
        log_info "请先运行 generate-secrets.sh 生成安全密钥"
        exit 1
    fi
    
    # 检查关键环境变量
    source .env
    
    local missing_vars=()
    
    if [ -z "$N8N_ENCRYPTION_KEY" ]; then
        missing_vars+=("N8N_ENCRYPTION_KEY")
    fi
    
    if [ -z "$N8N_USER_MANAGEMENT_JWT_SECRET" ]; then
        missing_vars+=("N8N_USER_MANAGEMENT_JWT_SECRET")
    fi
    
    if [ -z "$POSTIZ_JWT_SECRET" ]; then
        missing_vars+=("POSTIZ_JWT_SECRET")
    fi
    
    if [ -z "$N8N_DB_POSTGRESDB_PASSWORD" ]; then
        missing_vars+=("N8N_DB_POSTGRESDB_PASSWORD")
    fi
    
    if [ -z "$POSTIZ_POSTGRES_PASSWORD" ]; then
        missing_vars+=("POSTIZ_POSTGRES_PASSWORD")
    fi
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        log_error "缺少以下环境变量:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        log_info "请运行 generate-secrets.sh 生成安全密钥并更新 .env 文件"
        exit 1
    fi
    
    log_success "环境变量检查通过"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 首先启动数据库服务
    log_info "启动数据库服务..."
    docker compose up -d n8n-postgres postiz-postgres postiz-redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 10
    
    # 启动应用服务
    log_info "启动应用服务..."
    docker compose up -d n8n postiz
    
    # 等待应用启动
    log_info "等待应用启动..."
    sleep 15
    
    # 启动代理服务
    log_info "启动代理服务..."
    docker compose up -d caddy
    
    log_success "所有服务已启动"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    docker compose ps
    
    log_info "等待服务完全启动..."
    sleep 30
    
    # 检查服务健康状态
    local unhealthy_services=()
    
    while IFS= read -r line; do
        if echo "$line" | grep -q "unhealthy"; then
            service_name=$(echo "$line" | awk '{print $1}')
            unhealthy_services+=("$service_name")
        fi
    done < <(docker compose ps --format "table {{.Name}}\t{{.Status}}")
    
    if [ ${#unhealthy_services[@]} -gt 0 ]; then
        log_warning "以下服务状态异常:"
        for service in "${unhealthy_services[@]}"; do
            echo "  - $service"
        done
        log_info "请检查日志: ./manage.sh logs [service_name]"
    else
        log_success "所有服务运行正常"
    fi
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "=============================================="
    echo "🎉 部署完成！"
    echo "=============================================="
    echo ""
    echo "服务访问地址:"
    echo "  📊 n8n:     https://n8n.eric-n8n.online"
    echo "  📱 Postiz: https://postiz.eric-n8n.online"
    echo ""
    echo "管理命令:"
    echo "  查看状态: ./manage.sh status"
    echo "  查看日志: ./manage.sh logs [service]"
    echo "  重启服务: ./manage.sh restart [service]"
    echo "  停止服务: ./manage.sh stop"
    echo "  备份数据: ./manage.sh backup"
    echo ""
    echo "重要提醒:"
    echo "  1. 确保域名 DNS 已正确配置"
    echo "  2. 首次访问可能需要等待 SSL 证书生成"
    echo "  3. 定期备份数据和配置文件"
    echo "  4. 监控服务日志和资源使用情况"
    echo ""
    echo "=============================================="
}

# 主函数
main() {
    echo "=============================================="
    echo "🚀 自动化技术栈一键部署"
    echo "=============================================="
    echo ""
    
    check_user
    check_docker
    setup_project
    setup_docker_resources
    check_env_vars
    start_services
    check_services
    show_deployment_info
}

# 运行主函数
main "$@"
