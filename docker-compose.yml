version: '3.8'

# =============================================================================
# 自动化技术栈 Docker Compose 配置
# 包含 n8n、Postiz、数据库、Redis 和 Caddy 反向代理
# =============================================================================

services:
  # =============================================================================
  # Caddy 反向代理服务
  # =============================================================================
  caddy:
    image: caddy:2-alpine
    container_name: automation-caddy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "2019:2019"  # Caddy 管理 API
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile:ro
      - caddy_data:/data
      - caddy_config:/config
      - ./logs/caddy:/var/log/caddy
    networks:
      - proxy-network
    environment:
      - CADDY_EMAIL=${CADDY_EMAIL:-<EMAIL>}
    healthcheck:
      test: ["CMD", "caddy", "version"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # n8n 服务
  # =============================================================================
  n8n:
    image: n8nio/n8n:latest
    container_name: automation-n8n
    restart: unless-stopped
    environment:
      # 基本配置
      - N8N_HOST=${N8N_DOMAIN:-n8n.eric-n8n.online}
      - N8N_PORT=5678
      - N8N_PROTOCOL=https
      - WEBHOOK_URL=https://${N8N_DOMAIN:-n8n.eric-n8n.online}/
      
      # 数据库配置
      - DB_TYPE=${N8N_DB_TYPE:-postgresdb}
      - DB_POSTGRESDB_HOST=${N8N_DB_POSTGRESDB_HOST:-n8n-postgres}
      - DB_POSTGRESDB_PORT=${N8N_DB_POSTGRESDB_PORT:-5432}
      - DB_POSTGRESDB_DATABASE=${N8N_DB_POSTGRESDB_DATABASE:-n8n}
      - DB_POSTGRESDB_USER=${N8N_DB_POSTGRESDB_USER:-n8n_user}
      - DB_POSTGRESDB_PASSWORD=${N8N_DB_POSTGRESDB_PASSWORD}
      
      # 安全配置
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY}
      - N8N_USER_MANAGEMENT_JWT_SECRET=${N8N_USER_MANAGEMENT_JWT_SECRET}
      
      # 功能配置
      - N8N_METRICS=${N8N_METRICS:-true}
      - N8N_LOG_LEVEL=${N8N_LOG_LEVEL:-info}
      - N8N_LOG_OUTPUT=${N8N_LOG_OUTPUT:-console,file}
      - N8N_LOG_FILE_LOCATION=/home/<USER>/.n8n/logs/
      - EXECUTIONS_DATA_PRUNE=${EXECUTIONS_DATA_PRUNE:-true}
      - EXECUTIONS_DATA_MAX_AGE=${EXECUTIONS_DATA_MAX_AGE:-168}
      
      # 时区配置
      - TZ=${TIMEZONE:-Asia/Shanghai}
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./n8n/local_files:/files
      - ./logs/n8n:/home/<USER>/.n8n/logs
    networks:
      - n8n-network
      - proxy-network
    depends_on:
      n8n-postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # n8n PostgreSQL 数据库
  # =============================================================================
  n8n-postgres:
    image: postgres:15-alpine
    container_name: automation-n8n-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${N8N_POSTGRES_DB:-n8n}
      - POSTGRES_USER=${N8N_POSTGRES_USER:-n8n_user}
      - POSTGRES_PASSWORD=${N8N_POSTGRES_PASSWORD}
      - TZ=${TIMEZONE:-Asia/Shanghai}
    volumes:
      - n8n_postgres_data:/var/lib/postgresql/data
    networks:
      - n8n-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${N8N_POSTGRES_USER:-n8n_user} -d ${N8N_POSTGRES_DB:-n8n}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # =============================================================================
  # Postiz 服务
  # =============================================================================
  postiz:
    image: ghcr.io/gitroomhq/postiz-app:latest
    container_name: automation-postiz
    restart: unless-stopped
    environment:
      # 基本配置
      - MAIN_URL=${POSTIZ_MAIN_URL:-https://postiz.eric-n8n.online}
      - FRONTEND_URL=${POSTIZ_FRONTEND_URL:-https://postiz.eric-n8n.online}
      - NEXT_PUBLIC_BACKEND_URL=${POSTIZ_NEXT_PUBLIC_BACKEND_URL:-https://postiz.eric-n8n.online/api}
      
      # 安全配置
      - JWT_SECRET=${POSTIZ_JWT_SECRET}
      - IS_GENERAL=${POSTIZ_IS_GENERAL:-true}
      - DISABLE_REGISTRATION=${POSTIZ_DISABLE_REGISTRATION:-false}
      
      # 数据库配置
      - DATABASE_URL=${POSTIZ_DATABASE_URL}
      - REDIS_URL=${POSTIZ_REDIS_URL:-redis://postiz-redis:6379}
      - BACKEND_INTERNAL_URL=${POSTIZ_BACKEND_INTERNAL_URL:-http://localhost:3000}
      
      # 存储配置
      - STORAGE_PROVIDER=${POSTIZ_STORAGE_PROVIDER:-local}
      - UPLOAD_DIRECTORY=${POSTIZ_UPLOAD_DIRECTORY:-/uploads}
      - NEXT_PUBLIC_UPLOAD_DIRECTORY=${POSTIZ_NEXT_PUBLIC_UPLOAD_DIRECTORY:-/uploads}
      
      # 时区配置
      - TZ=${TIMEZONE:-Asia/Shanghai}
    volumes:
      - postiz_data:/config/
      - postiz_uploads:/uploads/
    networks:
      - postiz-network
      - proxy-network
    depends_on:
      postiz-postgres:
        condition: service_healthy
      postiz-redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # Postiz PostgreSQL 数据库
  # =============================================================================
  postiz-postgres:
    image: postgres:17-alpine
    container_name: automation-postiz-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${POSTIZ_POSTGRES_DB:-postiz}
      - POSTGRES_USER=${POSTIZ_POSTGRES_USER:-postiz_user}
      - POSTGRES_PASSWORD=${POSTIZ_POSTGRES_PASSWORD}
      - TZ=${TIMEZONE:-Asia/Shanghai}
    volumes:
      - postiz_postgres_data:/var/lib/postgresql/data
    networks:
      - postiz-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTIZ_POSTGRES_USER:-postiz_user} -d ${POSTIZ_POSTGRES_DB:-postiz}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # =============================================================================
  # Postiz Redis 缓存
  # =============================================================================
  postiz-redis:
    image: redis:7.2-alpine
    container_name: automation-postiz-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    environment:
      - TZ=${TIMEZONE:-Asia/Shanghai}
    volumes:
      - postiz_redis_data:/data
    networks:
      - postiz-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

# =============================================================================
# Docker 数据卷配置
# =============================================================================
volumes:
  # n8n 相关数据卷
  n8n_data:
    external: true
  n8n_postgres_data:
    external: true

  # Postiz 相关数据卷
  postiz_data:
    external: true
  postiz_postgres_data:
    external: true
  postiz_redis_data:
    external: true
  postiz_uploads:
    external: true

  # Caddy 相关数据卷
  caddy_data:
    external: true
  caddy_config:
    external: true

# =============================================================================
# Docker 网络配置
# =============================================================================
networks:
  # n8n 内部网络
  n8n-network:
    external: true

  # Postiz 内部网络
  postiz-network:
    external: true

  # 代理网络（Caddy 访问服务）
  proxy-network:
    external: true
