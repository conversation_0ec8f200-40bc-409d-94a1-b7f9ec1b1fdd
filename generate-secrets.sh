#!/bin/bash

# 生成安全密钥脚本
# 为 n8n 和 Postiz 生成安全的随机密钥

set -e

echo "=== 生成安全密钥 ==="

# 检查 openssl 是否可用
if ! command -v openssl &> /dev/null; then
    echo "❌ openssl 未安装，请先安装 openssl"
    exit 1
fi

echo "生成随机密钥..."

# 生成各种密钥
N8N_ENCRYPTION_KEY=$(openssl rand -hex 16)
N8N_JWT_SECRET=$(openssl rand -base64 32)
POSTIZ_JWT_SECRET=$(openssl rand -base64 32)
N8N_DB_PASSWORD=$(openssl rand -base64 24 | tr -d "=+/" | cut -c1-20)
POSTIZ_DB_PASSWORD=$(openssl rand -base64 24 | tr -d "=+/" | cut -c1-20)

echo ""
echo "=== 生成的安全密钥 ==="
echo ""
echo "n8n 加密密钥:"
echo "N8N_ENCRYPTION_KEY=$N8N_ENCRYPTION_KEY"
echo ""
echo "n8n JWT 密钥:"
echo "N8N_USER_MANAGEMENT_JWT_SECRET=$N8N_JWT_SECRET"
echo ""
echo "Postiz JWT 密钥:"
echo "POSTIZ_JWT_SECRET=$POSTIZ_JWT_SECRET"
echo ""
echo "n8n 数据库密码:"
echo "N8N_DB_POSTGRESDB_PASSWORD=$N8N_DB_PASSWORD"
echo "N8N_POSTGRES_PASSWORD=$N8N_DB_PASSWORD"
echo ""
echo "Postiz 数据库密码:"
echo "POSTIZ_POSTGRES_PASSWORD=$POSTIZ_DB_PASSWORD"
echo ""

# 创建安全配置文件
cat > "$HOME/automation-stack/secrets.env" << EOF
# 自动生成的安全密钥
# 生成时间: $(date)

# n8n 安全配置
N8N_ENCRYPTION_KEY=$N8N_ENCRYPTION_KEY
N8N_USER_MANAGEMENT_JWT_SECRET=$N8N_JWT_SECRET
N8N_DB_POSTGRESDB_PASSWORD=$N8N_DB_PASSWORD
N8N_POSTGRES_PASSWORD=$N8N_DB_PASSWORD

# Postiz 安全配置
POSTIZ_JWT_SECRET=$POSTIZ_JWT_SECRET
POSTIZ_POSTGRES_PASSWORD=$POSTIZ_DB_PASSWORD

# 更新数据库连接字符串
POSTIZ_DATABASE_URL=*****************************************************************/postiz
EOF

# 设置文件权限
chmod 600 "$HOME/automation-stack/secrets.env"

echo ""
echo "✓ 安全密钥已生成并保存到: $HOME/automation-stack/secrets.env"
echo ""
echo "⚠️ 重要提醒："
echo "1. 请将这些密钥复制到您的 .env 文件中"
echo "2. 删除 secrets.env 文件或确保其安全存储"
echo "3. 不要将密钥分享给他人"
echo ""
echo "下一步: 请手动更新 .env 文件中的密钥值"
