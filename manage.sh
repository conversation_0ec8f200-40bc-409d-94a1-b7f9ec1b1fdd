#!/bin/bash

# =============================================================================
# 自动化技术栈管理脚本
# 用于管理 n8n 和 Postiz 服务的启动、停止、更新等操作
# =============================================================================

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$HOME/automation-stack"
COMPOSE_FILE="$PROJECT_DIR/docker-compose.yml"
ENV_FILE="$PROJECT_DIR/.env"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查环境..."
    
    # 检查是否在正确的目录
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "Docker Compose 文件不存在: $COMPOSE_FILE"
        log_info "请确保在正确的项目目录中运行此脚本"
        exit 1
    fi
    
    # 检查环境变量文件
    if [ ! -f "$ENV_FILE" ]; then
        log_error "环境变量文件不存在: $ENV_FILE"
        exit 1
    fi
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 显示服务状态
show_status() {
    log_info "显示服务状态..."
    cd "$PROJECT_DIR"
    docker compose ps
}

# 启动所有服务
start_all() {
    log_info "启动所有服务..."
    cd "$PROJECT_DIR"
    docker compose up -d
    log_success "所有服务已启动"
    show_status
}

# 停止所有服务
stop_all() {
    log_info "停止所有服务..."
    cd "$PROJECT_DIR"
    docker compose down
    log_success "所有服务已停止"
}

# 重启所有服务
restart_all() {
    log_info "重启所有服务..."
    stop_all
    start_all
}

# 启动单个服务
start_service() {
    local service=$1
    if [ -z "$service" ]; then
        log_error "请指定服务名称"
        return 1
    fi
    
    log_info "启动服务: $service"
    cd "$PROJECT_DIR"
    docker compose up -d "$service"
    log_success "服务 $service 已启动"
}

# 停止单个服务
stop_service() {
    local service=$1
    if [ -z "$service" ]; then
        log_error "请指定服务名称"
        return 1
    fi
    
    log_info "停止服务: $service"
    cd "$PROJECT_DIR"
    docker compose stop "$service"
    log_success "服务 $service 已停止"
}

# 重启单个服务
restart_service() {
    local service=$1
    if [ -z "$service" ]; then
        log_error "请指定服务名称"
        return 1
    fi
    
    log_info "重启服务: $service"
    cd "$PROJECT_DIR"
    docker compose restart "$service"
    log_success "服务 $service 已重启"
}

# 查看日志
show_logs() {
    local service=$1
    local lines=${2:-100}
    
    cd "$PROJECT_DIR"
    if [ -z "$service" ]; then
        log_info "显示所有服务日志 (最近 $lines 行)..."
        docker compose logs --tail="$lines" -f
    else
        log_info "显示服务 $service 日志 (最近 $lines 行)..."
        docker compose logs --tail="$lines" -f "$service"
    fi
}

# 更新服务
update_service() {
    local service=$1
    
    log_info "更新服务..."
    cd "$PROJECT_DIR"
    
    if [ -n "$service" ]; then
        log_info "更新单个服务: $service"
        docker compose pull "$service"
        docker compose up -d "$service"
        log_success "服务 $service 更新完成"
    else
        log_info "更新所有服务"
        docker compose pull
        docker compose up -d
        log_success "所有服务更新完成"
    fi
}

# 备份数据
backup_data() {
    local backup_dir="$PROJECT_DIR/backups/$(date +%Y%m%d_%H%M%S)"
    
    log_info "创建数据备份..."
    mkdir -p "$backup_dir"
    
    # 备份 Docker 数据卷
    log_info "备份 Docker 数据卷..."
    docker run --rm -v n8n_data:/data -v "$backup_dir":/backup alpine tar czf /backup/n8n_data.tar.gz -C /data .
    docker run --rm -v postiz_data:/data -v "$backup_dir":/backup alpine tar czf /backup/postiz_data.tar.gz -C /data .
    docker run --rm -v postiz_uploads:/data -v "$backup_dir":/backup alpine tar czf /backup/postiz_uploads.tar.gz -C /data .
    
    # 备份数据库
    log_info "备份数据库..."
    cd "$PROJECT_DIR"
    docker compose exec -T n8n-postgres pg_dump -U n8n_user n8n > "$backup_dir/n8n_database.sql"
    docker compose exec -T postiz-postgres pg_dump -U postiz_user postiz > "$backup_dir/postiz_database.sql"
    
    # 备份配置文件
    log_info "备份配置文件..."
    cp "$ENV_FILE" "$backup_dir/"
    cp "$COMPOSE_FILE" "$backup_dir/"
    cp "$PROJECT_DIR/Caddyfile" "$backup_dir/"
    
    log_success "备份完成: $backup_dir"
}

# 显示帮助信息
show_help() {
    echo "自动化技术栈管理脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  status                    显示服务状态"
    echo "  start [service]           启动所有服务或指定服务"
    echo "  stop [service]            停止所有服务或指定服务"
    echo "  restart [service]         重启所有服务或指定服务"
    echo "  logs [service] [lines]    查看日志 (默认100行)"
    echo "  update [service]          更新所有服务或指定服务"
    echo "  backup                    备份数据和配置"
    echo "  help                      显示此帮助信息"
    echo ""
    echo "服务名称:"
    echo "  caddy                     Caddy 反向代理"
    echo "  n8n                       n8n 自动化平台"
    echo "  n8n-postgres              n8n 数据库"
    echo "  postiz                    Postiz 社交媒体管理"
    echo "  postiz-postgres           Postiz 数据库"
    echo "  postiz-redis              Postiz 缓存"
    echo ""
    echo "示例:"
    echo "  $0 start                  启动所有服务"
    echo "  $0 restart n8n            重启 n8n 服务"
    echo "  $0 logs postiz 50         查看 Postiz 最近50行日志"
    echo "  $0 update                 更新所有服务"
}

# 主函数
main() {
    case "${1:-help}" in
        "status")
            check_environment
            show_status
            ;;
        "start")
            check_environment
            if [ -n "$2" ]; then
                start_service "$2"
            else
                start_all
            fi
            ;;
        "stop")
            check_environment
            if [ -n "$2" ]; then
                stop_service "$2"
            else
                stop_all
            fi
            ;;
        "restart")
            check_environment
            if [ -n "$2" ]; then
                restart_service "$2"
            else
                restart_all
            fi
            ;;
        "logs")
            check_environment
            show_logs "$2" "$3"
            ;;
        "update")
            check_environment
            update_service "$2"
            ;;
        "backup")
            check_environment
            backup_data
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 运行主函数
main "$@"
