#!/bin/bash

# 检查和配置用户环境脚本
# 用于在 DigitalOcean 服务器上设置 xubin 用户

set -e

echo "=== 检查和配置用户环境 ==="

# 检查是否以 root 用户运行
if [ "$EUID" -ne 0 ]; then
    echo "请以 root 用户运行此脚本"
    echo "使用: sudo bash setup-user.sh"
    exit 1
fi

USERNAME="xubin"

# 检查用户是否存在
if id "$USERNAME" &>/dev/null; then
    echo "✓ 用户 $USERNAME 已存在"
else
    echo "创建用户 $USERNAME..."
    adduser --disabled-password --gecos "" $USERNAME
    echo "✓ 用户 $USERNAME 创建成功"
fi

# 检查用户是否在 sudo 组中
if groups $USERNAME | grep -q '\bsudo\b'; then
    echo "✓ 用户 $USERNAME 已在 sudo 组中"
else
    echo "将用户 $USERNAME 添加到 sudo 组..."
    usermod -aG sudo $USERNAME
    echo "✓ 用户 $USERNAME 已添加到 sudo 组"
fi

# 检查 SSH 目录和公钥
USER_HOME="/home/<USER>"
SSH_DIR="$USER_HOME/.ssh"
AUTHORIZED_KEYS="$SSH_DIR/authorized_keys"

if [ ! -d "$SSH_DIR" ]; then
    echo "创建 SSH 目录..."
    mkdir -p "$SSH_DIR"
    chown $USERNAME:$USERNAME "$SSH_DIR"
    chmod 700 "$SSH_DIR"
    echo "✓ SSH 目录创建成功"
fi

if [ ! -f "$AUTHORIZED_KEYS" ]; then
    echo "创建 authorized_keys 文件..."
    touch "$AUTHORIZED_KEYS"
    chown $USERNAME:$USERNAME "$AUTHORIZED_KEYS"
    chmod 600 "$AUTHORIZED_KEYS"
    echo "✓ authorized_keys 文件创建成功"
    echo ""
    echo "⚠️  请手动添加您的 SSH 公钥到以下文件："
    echo "   $AUTHORIZED_KEYS"
    echo ""
    echo "您可以使用以下命令添加公钥："
    echo "   echo 'your-ssh-public-key-here' >> $AUTHORIZED_KEYS"
else
    echo "✓ authorized_keys 文件已存在"
fi

# 检查 Docker 是否安装
if command -v docker &> /dev/null; then
    echo "✓ Docker 已安装"
    
    # 将用户添加到 docker 组
    if groups $USERNAME | grep -q '\bdocker\b'; then
        echo "✓ 用户 $USERNAME 已在 docker 组中"
    else
        echo "将用户 $USERNAME 添加到 docker 组..."
        usermod -aG docker $USERNAME
        echo "✓ 用户 $USERNAME 已添加到 docker 组"
    fi
else
    echo "⚠️  Docker 未安装，请先安装 Docker"
fi

# 检查 Docker Compose 是否安装
if command -v docker-compose &> /dev/null || docker compose version &> /dev/null; then
    echo "✓ Docker Compose 已安装"
else
    echo "⚠️  Docker Compose 未安装，请先安装 Docker Compose"
fi

echo ""
echo "=== 用户环境配置完成 ==="
echo "用户名: $USERNAME"
echo "用户主目录: $USER_HOME"
echo "SSH 目录: $SSH_DIR"
echo ""
echo "下一步："
echo "1. 确保您的 SSH 公钥已添加到 $AUTHORIZED_KEYS"
echo "2. 使用 'su - $USERNAME' 切换到 $USERNAME 用户"
echo "3. 或者直接使用 SSH 连接: ssh $USERNAME@your-server-ip"
